using Furion.DependencyInjection;
using Furion.DynamicApiController;
using IotPlatform.DongleLicense.Models;
using Microsoft.AspNetCore.Mvc;
using System.Security.Cryptography;
using System.Text;

namespace IotPlatform.DongleLicense.Services;

/// <summary>
/// 加密锁许可证管理服务
/// 版 本:V5.0.0
/// 版 权:杭州峰回科技有限公司
/// 作 者:系统生成
/// 日 期:2024-01-20
/// </summary>
[ApiDescriptionSettings("加密锁许可证")]
public class DongleLicenseService : IDynamicApiController, ITransient
{
    private readonly DongleCheckService _checkService;
    private readonly DongleLicenseManager _licenseManager;
    private readonly DongleTriggerService _triggerService;
    private readonly DongleAuthService _authService;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="checkService">检查服务</param>
    /// <param name="licenseManager">许可证管理器</param>
    /// <param name="triggerService">触发服务</param>
    /// <param name="authService">授权服务</param>
    public DongleLicenseService(
        DongleCheckService checkService,
        DongleLicenseManager licenseManager,
        DongleTriggerService triggerService,
        DongleAuthService authService)
    {
        _checkService = checkService;
        _licenseManager = licenseManager;
        _triggerService = triggerService;
        _authService = authService;
    }
    
    /// <summary>
    /// 手动触发检查
    /// </summary>
    /// <returns>检查结果</returns>
    [HttpPost("/dongleLicense/triggerCheck")]
    public async Task<DongleCheckResult> TriggerCheck()
    {
        return await _triggerService.TriggerCheckAsync();
    }

    /// <summary>
    /// 获取加密锁详细信息
    /// </summary>
    /// <returns>详细信息</returns>
    [HttpGet("/dongleLicense/detailInfo")]
    public async Task<string> GetDetailInfo()
    {
        return await _checkService.GetDongleDetailInfoAsync();
    }

    /// <summary>
    /// 获取加密锁设备列表
    /// </summary>
    /// <returns>设备列表</returns>
    [HttpGet("/dongleLicense/devices")]
    public async Task<dynamic> GetDevices()
    {
        var checkResult = await _checkService.CheckDongleAsync();
        
        var devices = checkResult.DongleInfos.Select(d => new
        {
            d.Index,
            d.Version,
            d.ProductType,
            d.BirthDay,
            d.AgentId,
            d.ProductId,
            d.UserId,
            d.HardwareId,
            d.IsMother,
            d.DeviceType,
            d.CanOpenClose,
            d.OpenCloseError,
            Status = d.CanOpenClose ? "正常" : "异常"
        }).ToList();

        return new
        {
            checkTime = checkResult.CheckTime,
            isSuccess = checkResult.IsSuccess,
            deviceCount = checkResult.DongleCount,
            elapsedMs = checkResult.ElapsedMilliseconds,
            errorMessage = checkResult.ErrorMessage,
            devices
        };
    }

    /// <summary>
    /// 获取系统健康状态（包含加密锁状态）
    /// </summary>
    /// <returns>健康状态</returns>
    [HttpGet("/dongleLicense/health")]
    public dynamic GetHealth()
    {
        var lastResult = _licenseManager.LastCheckResult;
        var isRunning = _licenseManager.IsRunning;

        return new
        {
            service = new
            {
                isRunning = isRunning,
                status = isRunning ? "运行中" : "已停止"
            },
            lastCheck = lastResult != null ? new
            {
                time = lastResult.CheckTime,
                success = lastResult.IsSuccess,
                deviceCount = lastResult.DongleCount,
                message = lastResult.ErrorMessage,
                authStatus = lastResult.AuthorizationStatus
            } : null,
            overall = new
            {
                status = isRunning && (lastResult?.IsSuccess ?? false) && (lastResult?.IsAuthorized ?? false) ? "健康" : "异常",
                timestamp = DateTime.Now
            }
        };
    }

    /// <summary>
    /// 初始化授权密钥（一次性操作）
    /// </summary>
    /// <param name="input">初始化授权输入参数</param>
    /// <returns>初始化结果</returns>
    [HttpPost("/dongleLicense/initializeAuth")]
    public async Task<dynamic> InitializeAuthorization([FromBody] InitializeAuthInput input)
    {
        try
        {
            // 参数验证
            if (input == null || string.IsNullOrWhiteSpace(input.HardwareId))
            {
                return new
                {
                    success = false,
                    message = "硬件ID不能为空",
                    timestamp = DateTime.Now
                };
            }

            // 检查是否已经初始化过
            if (_authService.IsAuthInitialized())
            {
                return new
                {
                    success = false,
                    message = "授权密钥已存在，无法重复初始化",
                    timestamp = DateTime.Now
                };
            }

            // 保存用户传入的授权密钥到文件
            var saveResult = _authService.SaveAuthKey(input.HardwareId.Trim());
            if (!saveResult)
            {
                return new
                {
                    success = false,
                    message = "保存授权密钥失败",
                    timestamp = DateTime.Now
                };
            }

            return new
            {
                success = true,
                message = "授权密钥初始化成功",
                hardwareId = input.HardwareId.Trim(),
                timestamp = DateTime.Now
            };
        }
        catch (Exception ex)
        {
            return new
            {
                success = false,
                message = $"初始化授权密钥时发生异常: {ex.Message}",
                timestamp = DateTime.Now
            };
        }
    }

    /// <summary>
    /// 检查是否已初始化授权密钥
    /// </summary>
    /// <returns>授权初始化状态</returns>
    [HttpGet("/dongleLicense/hasAuthKey")]
    public dynamic HasAuthorizationKey()
    {
        var isInitialized = _authService.IsAuthInitialized();

        return new
        {
            hasAuthKey = isInitialized,
            isInitialized = isInitialized,
            message = isInitialized ? "已初始化授权密钥" : "未初始化授权密钥",
            timestamp = DateTime.Now
        };
    }

    /// <summary>
    /// 获取授权状态
    /// </summary>
    /// <returns>授权状态信息</returns>
    [HttpGet("/dongleLicense/authStatus")]
    public dynamic GetAuthorizationStatus()
    {
        var authInfo = _authService.GetAuthInfo();
        var lastResult = _licenseManager.LastCheckResult;

        return new
        {
            authInfo,
            lastAuthCheck = lastResult != null ? new
            {
                time = lastResult.CheckTime,
                isAuthorized = lastResult.IsAuthorized,
                authStatus = lastResult.AuthorizationStatus,
                currentHardwareId = lastResult.DongleInfos.FirstOrDefault()?.HardwareId
            } : null,
            timestamp = DateTime.Now
        };
    }


}
