using System.Diagnostics;
using System.Security.Cryptography;
using System.Text;
using IotPlatform.DongleLicense.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace IotPlatform.DongleLicense.Services;

/// <summary>
/// 加密锁检查服务
/// </summary>
public class DongleCheckService
{
    private readonly DongleApiService _dongleApiService;
    private readonly ILogger<DongleCheckService> _logger;
    private readonly DongleCheckConfig _config;
    private readonly DongleAuthService _authService;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="dongleApiService">加密锁API服务</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="config">配置选项</param>
    /// <param name="authService">授权服务</param>
    public DongleCheckService(
        DongleApiService dongleApiService,
        ILogger<DongleCheckService> logger,
        IOptions<DongleCheckConfig> config,
        DongleAuthService authService)
    {
        _dongleApiService = dongleApiService;
        _logger = logger;
        _config = config.Value;
        _authService = authService;
    }

    /// <summary>
    /// 执行加密锁检查
    /// </summary>
    /// <returns>检查结果</returns>
    public async Task<DongleCheckResult> CheckDongleAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new DongleCheckResult
        {
            CheckTime = DateTime.Now
        };

        try
        {
            if (_config.EnableDetailedLogging)
            {
                _logger.LogInformation("开始执行加密锁检查...");
            }

            // 第一次枚举：获取设备数量
            var (enumResult1, _, count) = _dongleApiService.EnumDongle();

            if (enumResult1 != 0)
            {
                result.IsSuccess = false;
                result.ErrorMessage = $"Enum Dongle Failed! Return value: {enumResult1:X}";
                _logger.LogError("枚举加密锁失败，错误代码: {ErrorCode}", enumResult1.ToString("X"));
                return result;
            }

            if (_config.EnableDetailedLogging)
            {
                _logger.LogInformation("Enum Dongle Success! Count: {Count}", count);
            }

            result.DongleCount = count;

            if (count == 0)
            {
                result.IsSuccess = false;
                result.ErrorMessage = "未检测到加密锁设备";
                _logger.LogWarning("未检测到加密锁设备");
                return result;
            }

            // 第二次枚举：获取设备详细信息
            var (enumResult2, dongleInfo, count2) = _dongleApiService.EnumDongle();

            if (enumResult2 != 0)
            {
                result.IsSuccess = false;
                result.ErrorMessage = $"GetInfo Dongle Failed! Return value: {enumResult2:X}";
                _logger.LogError("获取设备信息失败，错误代码: {ErrorCode}", enumResult2.ToString("X"));
                return result;
            }

            if (_config.EnableDetailedLogging)
            {
                _logger.LogInformation("GetInfo Dongle Success!");
            }

            // 处理每个设备（但只对第一个设备进行打开关闭测试，就像WinForm示例一样）
            for (int i = 0; i < count; i++)
            {
                var deviceInfo = await ProcessDongleDeviceAsync(dongleInfo, i, i == 0);
                result.DongleInfos.Add(deviceInfo);
            }

            if (_config.EnableDetailedLogging)
            {
                _logger.LogInformation("所有加密锁设备检查通过");
            }

            // 执行授权验证
            PerformAuthorizationCheck(result);

            result.IsSuccess = true;
        }
        catch (Exception ex)
        {
            result.IsSuccess = false;
            result.ErrorMessage = $"检查过程中发生异常: {ex.Message}";
            _logger.LogError(ex, "加密锁检查过程中发生异常");
        }
        finally
        {
            stopwatch.Stop();
            result.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
            
            if (_config.EnableDetailedLogging)
            {
                _logger.LogInformation("加密锁检查完成，耗时: {ElapsedMs}ms", result.ElapsedMilliseconds);
            }
        }

        return result;
    }

    /// <summary>
    /// 处理单个加密锁设备
    /// </summary>
    /// <param name="dongleInfo">设备信息</param>
    /// <param name="index">设备索引</param>
    /// <param name="testOpenClose">是否测试打开关闭（只对第一个设备测试）</param>
    /// <returns>设备信息</returns>
    private async Task<DongleDeviceInfo> ProcessDongleDeviceAsync(DongleApiService.DONGLE_INFO dongleInfo, int index, bool testOpenClose)
    {
        var deviceInfo = DongleDeviceInfo.FromDongleInfo(dongleInfo, index);

        // 只有配置启用且是第一个设备时才进行打开关闭测试（按照WinForm示例）
        if (!_config.TestOpenClose || !testOpenClose)
        {
            deviceInfo.CanOpenClose = true;
            return deviceInfo;
        }

        try
        {
            // 严格按照WinForm示例的变量声明方式
            long hDongle = 0;

            // 严格按照WinForm示例：只对索引0进行测试
            var (openResult, handle) = _dongleApiService.OpenDongle(0);
            hDongle = handle; // 保存句柄到局部变量

            if (openResult != 0)
            {
                deviceInfo.CanOpenClose = false;
                deviceInfo.OpenCloseError = $"Open Dongle Failed! Return value: {openResult:X}";
                if (_config.EnableDetailedLogging)
                {
                    _logger.LogError("打开设备失败，错误代码: {ErrorCode}", openResult.ToString("X"));
                }
                return deviceInfo;
            }

            if (_config.EnableDetailedLogging)
            {
                _logger.LogDebug("Open Dongle Success! Handle: {Handle:X}", hDongle);
            }

            try
            {
                // 测试关闭设备，使用保存的句柄（不使用延迟，严格按照WinForm示例）
                var closeResult = _dongleApiService.CloseDongle(hDongle);
                if (closeResult != 0)
                {
                    deviceInfo.CanOpenClose = false;
                    deviceInfo.OpenCloseError = $"Close Dongle Failed! Return value: {closeResult:X}";
                    if (_config.EnableDetailedLogging)
                    {
                        _logger.LogError("关闭设备失败，错误代码: {ErrorCode}, 句柄: {Handle:X}", closeResult.ToString("X"), hDongle);
                    }
                    return deviceInfo;
                }

                if (_config.EnableDetailedLogging)
                {
                    _logger.LogDebug("Close Dongle Success!");
                }

                deviceInfo.CanOpenClose = true;
            }
            catch (Exception ex)
            {
                deviceInfo.CanOpenClose = false;
                deviceInfo.OpenCloseError = $"关闭设备时发生异常: {ex.Message}";
                _logger.LogError(ex, "关闭设备时发生异常，句柄: {Handle:X}", hDongle);

                // 尝试强制关闭
                try
                {
                    _dongleApiService.CloseDongle(hDongle);
                }
                catch (Exception forceEx)
                {
                    _logger.LogWarning(forceEx, "强制关闭设备也失败了，句柄: {Handle:X}", hDongle);
                }
            }
        }
        catch (Exception ex)
        {
            deviceInfo.CanOpenClose = false;
            deviceInfo.OpenCloseError = $"测试过程中发生异常: {ex.Message}";
            _logger.LogError(ex, "设备打开关闭测试发生异常");
        }

        return deviceInfo;
    }

    /// <summary>
    /// 获取加密锁详细信息（用于调试和监控）
    /// </summary>
    /// <returns>详细信息字符串</returns>
    public async Task<string> GetDongleDetailInfoAsync()
    {
        var result = await CheckDongleAsync();
        var info = new System.Text.StringBuilder();
        
        info.AppendLine("=== 加密锁检查详细信息 ===");
        info.AppendLine($"检查时间: {result.CheckTime:yyyy-MM-dd HH:mm:ss}");
        info.AppendLine($"检查结果: {(result.IsSuccess ? "成功" : "失败")}");
        info.AppendLine($"设备数量: {result.DongleCount}");
        info.AppendLine($"检查耗时: {result.ElapsedMilliseconds}ms");
        
        if (!string.IsNullOrEmpty(result.ErrorMessage))
        {
            info.AppendLine($"错误信息: {result.ErrorMessage}");
        }
        
        info.AppendLine();
        
        foreach (var device in result.DongleInfos)
        {
            info.AppendLine($"--- 设备 {device.Index} ---");
            info.AppendLine($"版本: {device.Version}");
            info.AppendLine($"产品类型: {device.ProductType}");
            info.AppendLine($"出厂日期: {device.BirthDay}");
            info.AppendLine($"代理商ID: {device.AgentId}");
            info.AppendLine($"产品ID: {device.ProductId}");
            info.AppendLine($"用户ID: {device.UserId}");
            info.AppendLine($"硬件ID: {device.HardwareId}");
            info.AppendLine($"是否母锁: {(device.IsMother ? "是" : "否")}");
            info.AppendLine($"设备类型: {device.DeviceType}");
            info.AppendLine($"访问状态: {(device.CanOpenClose ? "正常" : "异常")}");
            
            if (!device.CanOpenClose && !string.IsNullOrEmpty(device.OpenCloseError))
            {
                info.AppendLine($"错误信息: {device.OpenCloseError}");
            }
            
            info.AppendLine();
        }
        
        return info.ToString();
    }

    /// <summary>
    /// 执行授权验证
    /// </summary>
    /// <param name="result">检查结果</param>
    private void PerformAuthorizationCheck(DongleCheckResult result)
    {
        try
        {
            // 获取当前第一个设备的HardwareId
            var firstDevice = result.DongleInfos.FirstOrDefault();
            if (firstDevice == null || string.IsNullOrEmpty(firstDevice.HardwareId))
            {
                result.IsAuthorized = false;
                result.AuthorizationStatus = "无法获取设备硬件ID";
                if (_config.EnableDetailedLogging)
                {
                    _logger.LogWarning("授权验证失败：无法获取设备硬件ID");
                }
                return;
            }

            // 使用授权服务进行验证
            var (isValid, status) = _authService.ValidateAuth(firstDevice.HardwareId);

            result.IsAuthorized = isValid;
            result.AuthorizationStatus = status;

            if (isValid)
            {
                if (_config.EnableDetailedLogging)
                {
                    _logger.LogInformation("授权验证通过，设备硬件ID匹配");
                }
            }
            else
            {
                _logger.LogWarning("授权验证失败：{Status}", status);
            }
        }
        catch (Exception ex)
        {
            result.IsAuthorized = false;
            result.AuthorizationStatus = $"授权验证异常: {ex.Message}";
            _logger.LogError(ex, "执行授权验证时发生异常");
        }
    }


}
